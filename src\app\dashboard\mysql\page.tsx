"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Database, 
  Server, 
  Table, 
  RefreshCw, 
  CheckCircle, 
  XCircle,
  Eye,
  Loader2
} from "lucide-react"

interface TableInfo {
  [key: string]: any
}

interface ConnectionStatus {
  success: boolean
  message: string
  tables: TableInfo[]
  database: string
  host: string
}

export default function MySQLDashboard() {
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const testConnection = async () => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await fetch('/api/mysql-test')
      const data = await response.json()
      
      if (response.ok) {
        setConnectionStatus(data)
      } else {
        setError(data.error || 'Error al conectar a la base de datos')
      }
    } catch (err) {
      setError('Error de red al intentar conectar')
      console.error('Error:', err)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    testConnection()
  }, [])

  const formatTableName = (tableObj: any) => {
    // El objeto puede tener diferentes claves dependiendo de MySQL
    const keys = Object.keys(tableObj)
    const tableKey = keys.find(key => key.toLowerCase().includes('table')) || keys[0]
    return tableObj[tableKey]
  }

  return (
    <div className="p-8 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">MySQL Database Connection</h1>
          <p className="text-gray-600 mt-1">Monitor and test your MySQL database connection</p>
        </div>
        <Button 
          onClick={testConnection} 
          disabled={loading}
          className="gap-2"
        >
          {loading ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            <RefreshCw className="w-4 h-4" />
          )}
          Test Connection
        </Button>
      </div>

      {/* Connection Status Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="w-5 h-5" />
            Connection Status
          </CardTitle>
          <CardDescription>
            Current status of your MySQL database connection
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading && (
            <div className="flex items-center gap-2 text-blue-600">
              <Loader2 className="w-4 h-4 animate-spin" />
              <span>Testing connection...</span>
            </div>
          )}
          
          {error && (
            <div className="flex items-center gap-2 text-red-600">
              <XCircle className="w-4 h-4" />
              <span>{error}</span>
            </div>
          )}
          
          {connectionStatus && (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                {connectionStatus.success ? (
                  <>
                    <CheckCircle className="w-5 h-5 text-green-600" />
                    <Badge variant="default" className="bg-green-100 text-green-800">
                      Connected
                    </Badge>
                  </>
                ) : (
                  <>
                    <XCircle className="w-5 h-5 text-red-600" />
                    <Badge variant="destructive">
                      Disconnected
                    </Badge>
                  </>
                )}
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <Server className="w-4 h-4 text-gray-500" />
                  <span className="font-medium">Host:</span>
                  <span className="font-mono">{connectionStatus.host}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Database className="w-4 h-4 text-gray-500" />
                  <span className="font-medium">Database:</span>
                  <span className="font-mono">{connectionStatus.database}</span>
                </div>
              </div>
              
              <div>
                <p className="text-sm text-gray-600 mb-2">{connectionStatus.message}</p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Tables List */}
      {connectionStatus?.success && connectionStatus.tables && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Table className="w-5 h-5" />
              Database Tables
            </CardTitle>
            <CardDescription>
              List of tables available in your database
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {connectionStatus.tables.map((table, index) => {
                const tableName = formatTableName(table)
                return (
                  <div 
                    key={index}
                    className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50"
                  >
                    <div className="flex items-center gap-2">
                      <Table className="w-4 h-4 text-gray-500" />
                      <span className="font-medium text-sm">{tableName}</span>
                    </div>
                    <Button variant="ghost" size="sm">
                      <Eye className="w-4 h-4" />
                    </Button>
                  </div>
                )
              })}
            </div>
            
            {connectionStatus.tables.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <Table className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>No tables found in the database</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Connection Info */}
      <Card>
        <CardHeader>
          <CardTitle>Connection Information</CardTitle>
          <CardDescription>
            Your MySQL database connection details
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 text-sm">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <span className="font-medium text-gray-700">Host:</span>
                <div className="font-mono text-blue-600">**************</div>
              </div>
              <div>
                <span className="font-medium text-gray-700">Port:</span>
                <div className="font-mono text-blue-600">3306</div>
              </div>
              <div>
                <span className="font-medium text-gray-700">Database:</span>
                <div className="font-mono text-blue-600">operaciones_tqw</div>
              </div>
              <div>
                <span className="font-medium text-gray-700">User:</span>
                <div className="font-mono text-blue-600">ncornejo</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}