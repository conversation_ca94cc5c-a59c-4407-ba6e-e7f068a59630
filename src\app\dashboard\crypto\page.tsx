"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Bitcoin, 
  TrendingUp, 
  TrendingDown, 
  DollarSign,
  BarChart3,
  PieChart,
  Activity,
  ArrowUpRight,
  ArrowDownRight,
  Plus
} from "lucide-react"

export default function CryptoDashboard() {
  return (
    <div className="p-8 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard de Desafío Tecnico</h1>
          <p className="text-gray-600 mt-1">Monitorea proyectos y desafíos técnicos</p>
        </div>
        <Button className="gap-2">
          <Plus className="w-4 h-4" />
          Nuevo Proyecto
        </Button>
      </div>

      {/* Estadísticas de Proyectos */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Proyectos</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">24</div>
            <p className="text-xs text-muted-foreground flex items-center gap-1">
              <TrendingUp className="w-3 h-3 text-green-600" />
              *****% este mes
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completados</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">18</div>
            <p className="text-xs text-muted-foreground">
              75% tasa de éxito
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">En Progreso</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">4</div>
            <p className="text-xs text-muted-foreground flex items-center gap-1">
              <TrendingUp className="w-3 h-3 text-green-600" />
              +2 este mes
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tecnologías</CardTitle>
            <Bitcoin className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12</div>
            <p className="text-xs text-muted-foreground">
              Diferentes tecnologías
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Proyectos Activos */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Proyectos Activos</CardTitle>
            <CardDescription>Proyectos técnicos en desarrollo</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                { name: "API RESTful", symbol: "Node.js", amount: "85%", value: "Alta", change: "*****%", price: "15 días" },
                { name: "Machine Learning", symbol: "Python", amount: "60%", value: "Media", change: "*****%", price: "30 días" },
                { name: "Blockchain", symbol: "Solidity", amount: "40%", value: "Media", change: "-2.34%", price: "45 días" },
                { name: "IoT Platform", symbol: "React", amount: "90%", value: "Alta", change: "*****%", price: "10 días" },
                { name: "Mobile App", symbol: "Flutter", amount: "75%", value: "Alta", change: "*****%", price: "20 días" },
              ].map((project, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                      <Bitcoin className="w-5 h-5 text-orange-600" />
                    </div>
                    <div>
                      <p className="font-medium">{project.name}</p>
                      <p className="text-sm text-gray-500">{project.amount} • {project.symbol}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">{project.value}</p>
                    <div className="flex items-center justify-end gap-1">
                      {project.change.startsWith("+") ? (
                        <TrendingUp className="w-3 h-3 text-green-600" />
                      ) : (
                        <TrendingDown className="w-3 h-3 text-red-600" />
                      )}
                      <span className={`text-xs ${
                        project.change.startsWith("+") ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {project.change}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Tecnologías Utilizadas</CardTitle>
            <CardDescription>Stack tecnológico actual</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                { name: "React.js", symbol: "Frontend", price: "Muy Popular", change: "*****%", volume: "85%" },
                { name: "Node.js", symbol: "Backend", price: "Popular", change: "*****%", volume: "78%" },
                { name: "Python", symbol: "ML/AI", price: "Muy Popular", change: "-2.34%", volume: "92%" },
                { name: "Docker", symbol: "DevOps", price: "Popular", change: "+8.90%", volume: "70%" },
                { name: "AWS", symbol: "Cloud", price: "Muy Popular", change: "-0.56%", volume: "88%" },
                { name: "MongoDB", symbol: "Database", price: "Popular", change: "*****%", volume: "65%" },
              ].map((tech, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                      <BarChart3 className="w-4 h-4 text-gray-600" />
                    </div>
                    <div>
                      <p className="font-medium">{tech.name}</p>
                      <p className="text-sm text-gray-500">{tech.symbol}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">{tech.price}</p>
                    <div className="flex items-center justify-end gap-1">
                      {tech.change.startsWith("+") ? (
                        <TrendingUp className="w-3 h-3 text-green-600" />
                      ) : (
                        <TrendingDown className="w-3 h-3 text-red-600" />
                      )}
                      <span className={`text-xs ${
                        tech.change.startsWith("+") ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {tech.change}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Distribución de Proyectos */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Distribución de Proyectos</CardTitle>
            <CardDescription>Distribución por tipo de proyecto</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="h-48 bg-gray-100 rounded-lg flex items-center justify-center">
                <PieChart className="w-8 h-8 text-gray-400" />
                <span className="ml-2 text-gray-500">Gráfico de Distribución</span>
              </div>
              <div className="space-y-3">
                {[
                  { name: "API Development", percentage: 35, value: "8 proyectos", color: "bg-orange-600" },
                  { name: "Machine Learning", percentage: 25, value: "6 proyectos", color: "bg-blue-600" },
                  { name: "Mobile Apps", percentage: 20, value: "5 proyectos", color: "bg-green-600" },
                  { name: "Blockchain", percentage: 15, value: "3 proyectos", color: "bg-purple-600" },
                  { name: "IoT", percentage: 5, value: "2 proyectos", color: "bg-gray-600" },
                ].map((item, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className={`w-3 h-3 ${item.color} rounded-full`}></div>
                      <span className="text-sm font-medium">{item.name}</span>
                    </div>
                    <div className="text-right">
                      <span className="text-sm font-medium">{item.percentage}%</span>
                      <span className="text-xs text-gray-500 ml-2">{item.value}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Actividad Reciente</CardTitle>
            <CardDescription>Últimas actividades de proyectos</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                { type: "Inicio", crypto: "API RESTful", amount: "15 días", value: "85%", date: "Hace 2 horas", status: "En Progreso" },
                { type: "Revisión", crypto: "Machine Learning", amount: "30 días", value: "60%", date: "Hace 1 día", status: "En Progreso" },
                { type: "Testing", crypto: "Blockchain", amount: "45 días", value: "40%", date: "Hace 2 días", status: "Pausado" },
                { type: "Despliegue", crypto: "IoT Platform", amount: "10 días", value: "90%", date: "Hace 3 días", status: "Completado" },
                { type: "Optimización", crypto: "Mobile App", amount: "20 días", value: "75%", date: "Hace 4 días", status: "En Progreso" },
              ].map((activity, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      activity.type === 'Inicio' ? 'bg-green-100' : 
                      activity.type === 'Revisión' ? 'bg-blue-100' :
                      activity.type === 'Testing' ? 'bg-yellow-100' :
                      activity.type === 'Despliegue' ? 'bg-purple-100' : 'bg-orange-100'
                    }`}>
                      {activity.type === 'Inicio' ? (
                        <ArrowUpRight className="w-4 h-4 text-green-600" />
                      ) : activity.type === 'Revisión' ? (
                        <Activity className="w-4 h-4 text-blue-600" />
                      ) : activity.type === 'Testing' ? (
                        <TrendingUp className="w-4 h-4 text-yellow-600" />
                      ) : activity.type === 'Despliegue' ? (
                        <ArrowDownRight className="w-4 h-4 text-purple-600" />
                      ) : (
                        <Bitcoin className="w-4 h-4 text-orange-600" />
                      )}
                    </div>
                    <div>
                      <p className="font-medium">{activity.type} {activity.crypto}</p>
                      <p className="text-sm text-gray-500">{activity.amount} • {activity.date}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">{activity.value}</p>
                    <Badge 
                      variant={activity.status === 'Completado' ? 'default' : 'secondary'} 
                      className="text-xs"
                    >
                      {activity.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}