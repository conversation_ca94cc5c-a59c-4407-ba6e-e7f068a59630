"use client"

import { useRouter, usePathname } from "next/navigation"
import { FuseSidebar } from "@/components/fuse-sidebar"
import { FuseHeader } from "@/components/fuse-header"

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const router = useRouter()
  const pathname = usePathname()

  // Determinar el item activo basado en la ruta actual
  const getActiveItem = () => {
    if (pathname === '/dashboard/project') return 'project'
    if (pathname === '/dashboard/analytics') return 'analytics'
    if (pathname === '/dashboard/finance') return 'finance'
    if (pathname === '/dashboard/crypto') return 'crypto'
    if (pathname === '/dashboard/mysql') return 'mysql'
    return 'project' // valor por defecto
  }

  const activeItem = getActiveItem()

  const handleNavigation = (itemId: string) => {
    const routes = {
      project: '/dashboard/project',
      analytics: '/dashboard/analytics',
      finance: '/dashboard/finance',
      crypto: '/dashboard/crypto',
      mysql: '/dashboard/mysql'
    }
    
    if (routes[itemId]) {
      router.push(routes[itemId])
    }
  }

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar con 100% del height - estático */}
      <FuseSidebar activeItem={activeItem} onNavigation={handleNavigation} />
      
      {/* Contenedor principal con header y contenido */}
      <div className="flex-1 flex flex-col">
        {/* Header estático */}
        <FuseHeader />
        {/* Contenido dinámico - solo esta parte cambia */}
        <main className="flex-1 overflow-auto">
          {children}
        </main>
      </div>
    </div>
  )
}