"use client"

import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  DollarSign, 
  TrendingUp, 
  TrendingDown, 
  CreditCard,
  Banknote,
  PiggyBank,
  ArrowUpRight,
  ArrowDownRight,
  Plus
} from "lucide-react"

export default function FinanceDashboard() {
  return (
    <div className="p-8 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard de Logística</h1>
          <p className="text-gray-600 mt-1">Gestiona tu cadena de suministro y envíos</p>
        </div>
        <Button className="gap-2">
          <Plus className="w-4 h-4" />
          Nuevo Envío
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Envíos</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1,247</div>
            <p className="text-xs text-muted-foreground flex items-center gap-1">
              <TrendingUp className="w-3 h-3 text-green-600" />
              +12.5% del último mes
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Entregas a Tiempo</CardTitle>
            <ArrowUpRight className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">96.8%</div>
            <p className="text-xs text-muted-foreground flex items-center gap-1">
              <TrendingUp className="w-3 h-3 text-green-600" />
              +2.3% del último mes
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Costos de Envío</CardTitle>
            <ArrowDownRight className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$45,231</div>
            <p className="text-xs text-muted-foreground flex items-center gap-1">
              <TrendingDown className="w-3 h-3 text-red-600" />
              +3.1% del último mes
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Inventario</CardTitle>
            <PiggyBank className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">8,547</div>
            <p className="text-xs text-muted-foreground flex items-center gap-1">
              <TrendingUp className="w-3 h-3 text-green-600" />
              +15.3% del último mes
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Envíos Recientes */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Envíos Recientes</CardTitle>
            <CardDescription>Tus últimas actividades logísticas</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                { name: "Envío Express", amount: "2 días", type: "entregado", date: "Hoy", category: "Express" },
                { name: "Carga Estándar", amount: "5 días", type: "en tránsito", date: "Ayer", category: "Estándar" },
                { name: "Envío Internacional", amount: "12 días", type: "entregado", date: "Hace 2 días", category: "Internacional" },
                { name: "Mercancía Peligrosa", amount: "8 días", type: "retenido", date: "Hace 3 días", category: "Especial" },
                { name: "Devolución Cliente", amount: "3 días", type: "entregado", date: "Hace 4 días", category: "Devolución" },
                { name: "Carga Frágil", amount: "6 días", type: "en tránsito", date: "Hace 5 días", category: "Especial" },
              ].map((shipment, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      shipment.type === 'entregado' ? 'bg-green-100' : 
                      shipment.type === 'en tránsito' ? 'bg-blue-100' : 'bg-red-100'
                    }`}>
                      {shipment.type === 'entregado' ? (
                        <ArrowUpRight className="w-4 h-4 text-green-600" />
                      ) : shipment.type === 'en tránsito' ? (
                        <CreditCard className="w-4 h-4 text-blue-600" />
                      ) : (
                        <ArrowDownRight className="w-4 h-4 text-red-600" />
                      )}
                    </div>
                    <div>
                      <p className="font-medium">{shipment.name}</p>
                      <p className="text-sm text-gray-500">{shipment.category} • {shipment.date}</p>
                    </div>
                  </div>
                  <div className={`font-medium ${
                    shipment.type === 'entregado' ? 'text-green-600' : 
                    shipment.type === 'en tránsito' ? 'text-blue-600' : 'text-red-600'
                  }`}>
                    {shipment.amount}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Envíos por Categoría</CardTitle>
            <CardDescription>Desglose de tus envíos por tipo</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-3">
                {[
                  { category: "Envíos Express", amount: "342", percentage: 35, color: "bg-blue-600" },
                  { category: "Envíos Estándar", amount: "234", percentage: 24, color: "bg-green-600" },
                  { category: "Envíos Internacionales", amount: "156", percentage: 16, color: "bg-yellow-600" },
                  { category: "Envíos Especiales", amount: "127", percentage: 13, color: "bg-purple-600" },
                  { category: "Devoluciones", amount: "98", percentage: 12, color: "bg-pink-600" },
                ].map((item, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className={`w-3 h-3 ${item.color} rounded-full`}></div>
                        <span className="text-sm font-medium">{item.category}</span>
                      </div>
                      <span className="text-sm font-medium">{item.amount}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`${item.color} h-2 rounded-full`} 
                        style={{ width: `${item.percentage}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="pt-4 border-t">
                <div className="flex items-center justify-between">
                  <span className="font-medium">Total Envíos</span>
                  <span className="font-bold text-lg">957</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Resumen de Rutas */}
      <Card>
        <CardHeader>
          <CardTitle>Resumen de Rutas</CardTitle>
          <CardDescription>Seguimiento de rutas y entregas</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[
              { category: "Ruta Norte", budget: "45", spent: "42", remaining: "3", progress: 93 },
              { category: "Ruta Sur", budget: "38", spent: "35", remaining: "3", progress: 92 },
              { category: "Ruta Este", budget: "52", spent: "48", remaining: "4", progress: 92 },
            ].map((route, index) => (
              <div key={index} className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium">{route.category}</h4>
                  <Badge variant={route.progress > 95 ? "destructive" : "secondary"}>
                    {route.progress}%
                  </Badge>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Planificadas: {route.budget}</span>
                    <span>Completadas: {route.spent}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${route.progress > 95 ? 'bg-red-600' : 'bg-blue-600'}`} 
                      style={{ width: `${route.progress}%` }}
                    />
                  </div>
                  <div className="text-sm text-gray-600">
                    Pendientes: {route.remaining}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}