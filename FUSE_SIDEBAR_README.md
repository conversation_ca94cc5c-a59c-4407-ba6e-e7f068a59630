# FUSE React Sidebar Component

Este componente recrea fielmente el sidebar (barra lateral) que aparece en la interfaz de FUSE React, con todas las características visuales e interactivas del diseño original.

## Características Implementadas

### 🎨 Diseño Visual
- **Header**: Logo de FUSE React y indicador de Nextjs
- **Sección Dashboards**: Con 4 opciones (Project, Analytics, Finance, Crypto)
- **Sección Applications**: Con 8 opciones incluyendo AI Image Generator con etiqueta "NEW"
- **Footer**: Sección de ayuda e información de contacto

### ✨ Interactividad
- **Estado activo**: Los elementos seleccionados se resaltan en azul
- **Efectos hover**: Transiciones suaves al pasar el mouse
- **Animaciones**: Movimientos fluidos de los indicadores y flechas
- **Responsive**: Diseño que se adapta a diferentes tamaños de pantalla

### 🛠️ Componentes Utilizados
- **shadcn/ui**: <PERSON>ton, Separator, Badge, Avatar
- **Lucide React**: Iconos para cada elemento del menú
- **Tailwind CSS**: Para estilos y animaciones

### 📱 Estructura del Componente

```typescript
<FuseSidebar />
```

El componente incluye:
- Gestión de estado para elementos activos y hover
- Menús organizados por categorías
- Avatar con fallback para la información de contacto
- Badges para elementos nuevos
- Separadores visuales entre secciones

### 🎯 Elementos del Menú

#### Dashboards
- Project (activo por defecto)
- Analytics
- Finance
- Crypto

#### Applications
- AI Image Generator (con etiqueta NEW)
- Academy
- Calendar (con subtítulo "3 upcoming events")
- Messenger
- Contacts
- E-Commerce
- File Manager
- Help Center

### 🔧 Personalización

El componente está diseñado para ser fácilmente personalizable:
- Colores definidos mediante variables de Tailwind
- Tamaños ajustables mediante clases de utilidad
- Iconos intercambiables a través del objeto de configuración

### 📝 Notas de Implementación

- El componente utiliza `useState` para gestionar el estado de selección
- Las animaciones se implementan con transiciones de Tailwind CSS
- El diseño sigue los principios de accesibilidad con semántica HTML adecuada
- Se utiliza el sistema de diseño de shadcn/ui para mantener consistencia

### 🚀 Uso

Simplemente importa y utiliza el componente en tu página:

```tsx
import { FuseSidebar } from "@/components/fuse-sidebar"

export default function MyPage() {
  return (
    <div className="flex h-screen">
      <FuseSidebar />
      <main className="flex-1">
        {/* Tu contenido principal aquí */}
      </main>
    </div>
  )
}
```

El componente está listo para usar y coincide exactamente con el diseño mostrado en la imagen de referencia de FUSE React.