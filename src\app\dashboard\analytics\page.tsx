"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  TrendingUp, 
  Users, 
  Eye, 
  MousePointer,
  Bar<PERSON>hart3,
  <PERSON><PERSON>hart,
  Activity,
  Download
} from "lucide-react"

export default function AnalyticsDashboard() {
  return (
    <div className="p-8 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard de Calidad Reactiva</h1>
          <p className="text-gray-600 mt-1">Monitorea métricas de calidad y rendimiento</p>
        </div>
        <Button className="gap-2">
          <Download className="w-4 h-4" />
          Exportar Reporte
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Inspecciones</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">45,231</div>
            <p className="text-xs text-muted-foreground">
              +12.5% del último mes
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Controles de Calidad</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">128,432</div>
            <p className="text-xs text-muted-foreground">
              +8.2% del último mes
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tasa de Cumplimiento</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">96.8%</div>
            <p className="text-xs text-muted-foreground">
              +0.5% del último mes
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">No Conformidades</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">3.2%</div>
            <p className="text-xs text-muted-foreground">
              -2.1% del último mes
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Resumen de Calidad</CardTitle>
            <CardDescription>Métricas de calidad para los últimos 30 días</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-blue-600 rounded-full"></div>
                  <span className="text-sm">Inspecciones</span>
                </div>
                <Badge variant="secondary">+12.5%</Badge>
              </div>
              <div className="h-48 bg-gray-100 rounded-lg flex items-center justify-center">
                <BarChart3 className="w-8 h-8 text-gray-400" />
                <span className="ml-2 text-gray-500">Gráfico de Calidad</span>
              </div>
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <p className="text-2xl font-bold">1,524</p>
                  <p className="text-xs text-gray-500">Promedio Diario</p>
                </div>
                <div>
                  <p className="text-2xl font-bold">2,847</p>
                  <p className="text-xs text-gray-500">Pico Máximo</p>
                </div>
                <div>
                  <p className="text-2xl font-bold">892</p>
                  <p className="text-xs text-gray-500">Mínimo Diario</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Distribución de Calidad</CardTitle>
            <CardDescription>Distribución de métricas por categorías</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="h-48 bg-gray-100 rounded-lg flex items-center justify-center">
                <PieChart className="w-8 h-8 text-gray-400" />
                <span className="ml-2 text-gray-500">Gráfico de Distribución</span>
              </div>
              <div className="space-y-3">
                {[
                  { category: "Alta Calidad", percentage: "35%", color: "bg-blue-600" },
                  { category: "Calidad Media", percentage: "42%", color: "bg-green-600" },
                  { category: "Baja Calidad", percentage: "18%", color: "bg-yellow-600" },
                  { category: "Crítico", percentage: "5%", color: "bg-red-600" },
                ].map((item, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className={`w-3 h-3 ${item.color} rounded-full`}></div>
                      <span className="text-sm">{item.category}</span>
                    </div>
                    <span className="text-sm font-medium">{item.percentage}</span>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Categories */}
      <Card>
        <CardHeader>
          <CardTitle>Categorías Principales</CardTitle>
          <CardDescription>Categorías con mejor desempeño de calidad</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[
              { category: "Producción", score: "96.8%", change: "+2.5%", area: "Línea Principal" },
              { category: "Ensamblaje", score: "94.2%", change: "+1.8%", area: "Área de Montaje" },
              { category: "Control Calidad", score: "98.1%", change: "+0.9%", area: "Inspección Final" },
              { category: "Embalaje", score: "92.7%", change: "+1.2%", area: "Área de Empaque" },
              { category: "Logística", score: "89.3%", change: "-0.8%", area: "Almacén" },
            ].map((item, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-gray-100 rounded flex items-center justify-center">
                    <Eye className="w-4 h-4 text-gray-600" />
                  </div>
                  <div>
                    <p className="font-medium">{item.area}</p>
                    <p className="text-sm text-gray-500">{item.category}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-medium">{item.score}</p>
                  <Badge variant={item.change.startsWith("+") ? "default" : "destructive"} className="text-xs">
                    {item.change}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}