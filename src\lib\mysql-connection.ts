import mysql from 'mysql2/promise'

// Configuración de la conexión a MySQL
const connectionConfig = {
  host: '**************',
  port: 3306,
  user: 'ncornejo',
  password: 'N1c0l7as17',
  database: 'operaciones_tqw',
  ssl: {
    rejectUnauthorized: false // Para conexiones remotas que puedan requerir SSL
  }
}

// Función para crear una conexión
export async function createConnection() {
  try {
    const connection = await mysql.createConnection(connectionConfig)
    console.log('Conexión a MySQL establecida correctamente')
    return connection
  } catch (error) {
    console.error('Error al conectar a MySQL:', error)
    throw error
  }
}

// Función para ejecutar consultas
export async function executeQuery(query: string, params: any[] = []) {
  let connection
  try {
    connection = await createConnection()
    const [rows] = await connection.execute(query, params)
    return rows
  } catch (error) {
    console.error('Error al ejecutar consulta:', error)
    throw error
  } finally {
    if (connection) {
      await connection.end()
    }
  }
}

// Función para probar la conexión
export async function testConnection() {
  try {
    const connection = await createConnection()
    await connection.ping()
    console.log('✅ Conexión a MySQL exitosa')
    
    // Obtener información de la base de datos
    const [rows] = await connection.execute('SELECT DATABASE() as db_name, VERSION() as version')
    console.log('📊 Información de la base de datos:', rows)
    
    await connection.end()
    return true
  } catch (error) {
    console.error('❌ Error al probar la conexión:', error)
    return false
  }
}

// Función para obtener las tablas de la base de datos
export async function getTables() {
  try {
    const rows = await executeQuery('SHOW TABLES')
    return rows
  } catch (error) {
    console.error('Error al obtener tablas:', error)
    throw error
  }
}

// Función para describir una tabla específica
export async function describeTable(tableName: string) {
  try {
    const rows = await executeQuery(`DESCRIBE ${tableName}`)
    return rows
  } catch (error) {
    console.error(`Error al describir la tabla ${tableName}:`, error)
    throw error
  }
}