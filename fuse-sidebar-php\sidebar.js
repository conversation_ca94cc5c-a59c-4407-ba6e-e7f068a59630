/**
 * FUSE React Sidebar - JavaScript
 * 
 * Este archivo contiene toda la lógica JavaScript para el sidebar de FUSE React.
 * Es independiente del framework PHP y puede ser usado con cualquier backend.
 * 
 * <AUTHOR> with Claude Code
 * @version 1.0
 */

class FuseSidebarJS {
    constructor(options = {}) {
        this.options = {
            activeItem: options.activeItem || 'project',
            navigationMode: options.navigationMode || 'spa', // 'spa', 'full', 'ajax'
            baseUrl: options.baseUrl || '/',
            onItemClick: options.onItemClick || null,
            onItemHover: options.onItemHover || null,
            debug: options.debug || false
        };
        
        this.activeItem = this.options.activeItem;
        this.hoveredItem = null;
        this.init();
    }
    
    init() {
        this.log('Initializing FUSE Sidebar...');
        this.bindEvents();
        this.setInitialState();
    }
    
    bindEvents() {
        // Event delegation for better performance
        document.addEventListener('click', (e) => {
            if (e.target.closest('.fuse-sidebar-item')) {
                const item = e.target.closest('.fuse-sidebar-item');
                const itemId = item.dataset.itemId;
                this.handleItemClick(itemId, item);
            }
        });
        
        document.addEventListener('mouseenter', (e) => {
            if (e.target.closest('.fuse-sidebar-item')) {
                const item = e.target.closest('.fuse-sidebar-item');
                const itemId = item.dataset.itemId;
                this.handleItemHover(itemId, item);
            }
        }, true);
        
        document.addEventListener('mouseleave', (e) => {
            if (e.target.closest('.fuse-sidebar-item')) {
                this.handleItemLeave();
            }
        }, true);
        
        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.target.closest('.fuse-sidebar-item')) {
                this.handleKeydown(e);
            }
        });
    }
    
    setInitialState() {
        this.setActiveItem(this.activeItem, false);
        this.log('Initial state set with active item:', this.activeItem);
    }
    
    handleItemClick(itemId, itemElement) {
        this.log('Item clicked:', itemId);
        
        // Prevent default if we're handling navigation
        if (this.options.navigationMode !== 'none') {
            event.preventDefault();
        }
        
        // Set active state
        this.setActiveItem(itemId);
        
        // Execute callback if provided
        if (typeof this.options.onItemClick === 'function') {
            this.options.onItemClick(itemId, itemElement);
        }
        
        // Handle navigation based on mode
        this.handleNavigation(itemId);
    }
    
    handleItemHover(itemId, itemElement) {
        this.hoveredItem = itemId;
        this.log('Item hovered:', itemId);
        
        // Add hover effect
        const hoverEffect = itemElement.querySelector('.fuse-sidebar-hover');
        if (hoverEffect && this.activeItem !== itemId) {
            hoverEffect.classList.add('opacity-100');
        }
        
        // Execute callback if provided
        if (typeof this.options.onItemHover === 'function') {
            this.options.onItemHover(itemId, itemElement);
        }
    }
    
    handleItemLeave() {
        if (this.hoveredItem) {
            const item = document.querySelector(`[data-item-id="${this.hoveredItem}"]`);
            if (item && this.activeItem !== this.hoveredItem) {
                const hoverEffect = item.querySelector('.fuse-sidebar-hover');
                if (hoverEffect) {
                    hoverEffect.classList.remove('opacity-100');
                }
            }
        }
        this.hoveredItem = null;
    }
    
    handleKeydown(e) {
        const item = e.target.closest('.fuse-sidebar-item');
        const itemId = item.dataset.itemId;
        
        switch (e.key) {
            case 'Enter':
            case ' ':
                e.preventDefault();
                this.handleItemClick(itemId, item);
                break;
            case 'ArrowUp':
            case 'ArrowDown':
                e.preventDefault();
                this.navigateWithKeyboard(e.key === 'ArrowUp' ? 'up' : 'down');
                break;
        }
    }
    
    navigateWithKeyboard(direction) {
        const items = Array.from(document.querySelectorAll('.fuse-sidebar-item'));
        const currentIndex = items.findIndex(item => item.dataset.itemId === this.activeItem);
        
        let nextIndex;
        if (direction === 'up') {
            nextIndex = currentIndex <= 0 ? items.length - 1 : currentIndex - 1;
        } else {
            nextIndex = currentIndex >= items.length - 1 ? 0 : currentIndex + 1;
        }
        
        const nextItem = items[nextIndex];
        if (nextItem) {
            nextItem.focus();
            this.handleItemClick(nextItem.dataset.itemId, nextItem);
        }
    }
    
    handleNavigation(itemId) {
        switch (this.options.navigationMode) {
            case 'spa':
                // Single Page Application - no page reload
                this.log('SPA navigation to:', itemId);
                break;
                
            case 'full':
                // Full page reload
                const url = itemId === 'project' ? this.options.baseUrl : `${this.options.baseUrl}${itemId}`;
                this.log('Full navigation to:', url);
                window.location.href = url;
                break;
                
            case 'ajax':
                // AJAX content loading
                this.loadContentAjax(itemId);
                break;
                
            case 'none':
                // No automatic navigation
                this.log('No navigation for:', itemId);
                break;
        }
    }
    
    loadContentAjax(itemId) {
        const url = itemId === 'project' ? this.options.baseUrl : `${this.options.baseUrl}${itemId}`;
        this.log('Loading content via AJAX from:', url);
        
        fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.text();
            })
            .then(html => {
                const mainElement = document.querySelector('main');
                if (mainElement) {
                    mainElement.innerHTML = html;
                    this.log('Content loaded successfully');
                }
            })
            .catch(error => {
                console.error('Error loading content:', error);
            });
    }
    
    setActiveItem(itemId, triggerCallback = true) {
        // Remove active class from all items
        document.querySelectorAll('.fuse-sidebar-item').forEach(item => {
            item.classList.remove('bg-blue-50', 'text-blue-700', 'shadow-sm');
            item.classList.add('text-gray-700', 'hover:bg-gray-100');
            
            // Update icon colors
            const iconContainer = item.querySelector('.flex.items-center.justify-center.w-5.h-5');
            if (iconContainer) {
                iconContainer.classList.remove('text-blue-600');
                iconContainer.classList.add('text-gray-500', 'group-hover:text-gray-700');
            }
            
            // Hide chevron
            const chevron = item.querySelector('.transition-all.duration-200.flex-shrink-0');
            if (chevron) {
                chevron.classList.remove('opacity-100', 'translate-x-0');
                chevron.classList.add('opacity-0', '-translate-x-2');
            }
            
            // Remove hover effect
            const hoverEffect = item.querySelector('.fuse-sidebar-hover');
            if (hoverEffect) {
                hoverEffect.classList.remove('opacity-100');
            }
        });
        
        // Add active class to selected item
        const selectedItem = document.querySelector(`[data-item-id="${itemId}"]`);
        if (selectedItem) {
            selectedItem.classList.remove('text-gray-700', 'hover:bg-gray-100');
            selectedItem.classList.add('bg-blue-50', 'text-blue-700', 'shadow-sm');
            
            // Update icon color
            const iconContainer = selectedItem.querySelector('.flex.items-center.justify-center.w-5.h-5');
            if (iconContainer) {
                iconContainer.classList.remove('text-gray-500', 'group-hover:text-gray-700');
                iconContainer.classList.add('text-blue-600');
            }
            
            // Show chevron
            const chevron = selectedItem.querySelector('.transition-all.duration-200.flex-shrink-0');
            if (chevron) {
                chevron.classList.remove('opacity-0', '-translate-x-2');
                chevron.classList.add('opacity-100', 'translate-x-0');
            }
        }
        
        this.activeItem = itemId;
        this.log('Active item set to:', itemId);
        
        // Update URL without reload if in SPA mode
        if (this.options.navigationMode === 'spa') {
            const url = itemId === 'project' ? this.options.baseUrl : `${this.options.baseUrl}${itemId}`;
            window.history.pushState({ itemId }, '', url);
        }
    }
    
    // Public methods
    setActive(itemId) {
        this.setActiveItem(itemId);
    }
    
    getActive() {
        return this.activeItem;
    }
    
    destroy() {
        // Remove event listeners
        document.removeEventListener('click', this.handleClick);
        document.removeEventListener('mouseenter', this.handleMouseEnter);
        document.removeEventListener('mouseleave', this.handleMouseLeave);
        document.removeEventListener('keydown', this.handleKeydown);
        
        this.log('FUSE Sidebar destroyed');
    }
    
    log(...args) {
        if (this.options.debug) {
            console.log('[FUSE Sidebar]', ...args);
        }
    }
}

// Global functions for backward compatibility
let fuseSidebarInstance = null;

function initFuseSidebar(options = {}) {
    fuseSidebarInstance = new FuseSidebarJS(options);
    return fuseSidebarInstance;
}

function setActiveItem(itemId) {
    if (fuseSidebarInstance) {
        fuseSidebarInstance.setActive(itemId);
    }
}

function setHoveredItem(itemId) {
    if (fuseSidebarInstance) {
        fuseSidebarInstance.handleItemHover(itemId, document.querySelector(`[data-item-id="${itemId}"]`));
    }
}

function clearHoveredItem() {
    if (fuseSidebarInstance) {
        fuseSidebarInstance.handleItemLeave();
    }
}

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Check if we should auto-initialize
    const autoInit = document.querySelector('.fuse-sidebar') !== null;
    
    if (autoInit && !fuseSidebarInstance) {
        // Get active item from URL or data attribute
        const path = window.location.pathname;
        const segments = path.split('/').filter(s => s);
        const activeItem = segments[segments.length - 1] || 'project';
        
        // Get configuration from data attributes
        const sidebarElement = document.querySelector('.fuse-sidebar');
        const navigationMode = sidebarElement?.dataset.navigationMode || 'spa';
        const baseUrl = sidebarElement?.dataset.baseUrl || '/';
        
        initFuseSidebar({
            activeItem: activeItem,
            navigationMode: navigationMode,
            baseUrl: baseUrl,
            debug: false
        });
    }
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FuseSidebarJS;
} else if (typeof window !== 'undefined') {
    window.FuseSidebarJS = FuseSidebarJS;
    window.initFuseSidebar = initFuseSidebar;
    window.setActiveItem = setActiveItem;
    window.setHoveredItem = setHoveredItem;
    window.clearHoveredItem = clearHoveredItem;
}