import { NextRequest, NextResponse } from 'next/server'
import { testConnection, getTables } from '@/lib/mysql-connection'

export async function GET(request: NextRequest) {
  try {
    // Probar la conexión
    const connectionOk = await testConnection()
    
    if (!connectionOk) {
      return NextResponse.json(
        { error: 'No se pudo establecer conexión a la base de datos MySQL' },
        { status: 500 }
      )
    }

    // Obtener las tablas disponibles
    const tables = await getTables()

    return NextResponse.json({
      success: true,
      message: 'Conexión a MySQL establecida correctamente',
      tables: tables,
      database: 'operaciones_tqw',
      host: '**************'
    })

  } catch (error) {
    console.error('Error en el endpoint de prueba MySQL:', error)
    return NextResponse.json(
      { 
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    )
  }
}