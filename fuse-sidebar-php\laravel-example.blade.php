{{-- FUSE React Sidebar - Laravel Blade Example --}}

{{-- Este es un ejemplo de cómo integrar el sidebar de FUSE React en una aplicación Laravel --}}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ config('app.name', 'FUSE React - Laravel') }}</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom Styles -->
    <style>
        [x-cloak] { display: none !important; }
    </style>
    
    <!-- Alpine.js para reactividad (opcional) -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
</head>
<body class="bg-gray-50" x-data="{ 
    activeItem: '{{ request()->segment(1) ?? 'project' }}', 
    hoveredItem: null,
    
    setActiveItem(itemId) {
        this.activeItem = itemId;
        
        // Aquí puedes agregar lógica de navegación
        if (itemId !== this.activeItem) {
            window.location.href = '/' + itemId;
        }
    },
    
    setHoveredItem(itemId) {
        this.hoveredItem = itemId;
    },
    
    clearHoveredItem() {
        this.hoveredItem = null;
    }
}" x-init="$watch('activeItem', value => console.log('Active item changed:', value))">
    
    <div class="flex h-screen">
        <!-- Sidebar Component -->
        <div class="fuse-sidebar w-64 bg-white border-r border-gray-200 flex flex-col h-full shadow-sm">
            <!-- Header -->
            <div class="p-4 border-b border-gray-200 bg-white">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-2">
                        <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center shadow-sm">
                            <span class="text-white font-bold text-sm">F</span>
                        </div>
                        <span class="font-semibold text-gray-900 text-sm">FUSE React</span>
                    </div>
                    <div class="flex items-center gap-1">
                        <div class="w-6 h-6 bg-black rounded flex items-center justify-center shadow-sm">
                            <span class="text-white text-xs font-bold">L</span>
                        </div>
                        <span class="text-xs text-gray-600 font-medium">Laravel</span>
                    </div>
                </div>
            </div>

            <!-- Content -->
            <div class="flex-1 overflow-y-auto bg-gray-50/30">
                <!-- Dashboards Section -->
                <div class="p-4">
                    <div class="mb-3">
                        <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider">
                            Dashboards
                        </h3>
                        <p class="text-xs text-gray-400 mt-1">
                            Unique dashboard designs
                        </p>
                    </div>
                    <nav class="space-y-1">
                        <!-- Project -->
                        <button
                            @click="setActiveItem('project')"
                            @mouseenter="setHoveredItem('project')"
                            @mouseleave="clearHoveredItem()"
                            class="fuse-sidebar-item w-full flex items-center gap-3 px-3 py-2.5 text-sm rounded-lg transition-all duration-200 group relative overflow-hidden"
                            :class="activeItem === 'project' ? 'bg-blue-50 text-blue-700 shadow-sm' : 'text-gray-700 hover:bg-gray-100'"
                            data-item-id="project"
                            title="Project Dashboard"
                        >
                            <div class="flex items-center justify-center w-5 h-5 rounded transition-colors"
                                 :class="activeItem === 'project' ? 'text-blue-600' : 'text-gray-500 group-hover:text-gray-700'">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
                                    <rect x="3" y="3" width="7" height="7"></rect>
                                    <rect x="14" y="3" width="7" height="7"></rect>
                                    <rect x="14" y="14" width="7" height="7"></rect>
                                    <rect x="3" y="14" width="7" height="7"></rect>
                                </svg>
                            </div>
                            <span class="flex-1 text-left font-medium">Project</span>
                            <div class="transition-all duration-200"
                                 :class="activeItem === 'project' ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-2'">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4 text-blue-600">
                                    <polyline points="9 18 15 12 9 6"></polyline>
                                </svg>
                            </div>
                            <div class="fuse-sidebar-hover absolute inset-0 bg-gradient-to-r from-blue-50/50 to-transparent transition-opacity duration-200"
                                 :class="hoveredItem === 'project' && activeItem !== 'project' ? 'opacity-100' : 'opacity-0'"></div>
                        </button>

                        <!-- Analytics -->
                        <button
                            @click="setActiveItem('analytics')"
                            @mouseenter="setHoveredItem('analytics')"
                            @mouseleave="clearHoveredItem()"
                            class="fuse-sidebar-item w-full flex items-center gap-3 px-3 py-2.5 text-sm rounded-lg transition-all duration-200 group relative overflow-hidden"
                            :class="activeItem === 'analytics' ? 'bg-blue-50 text-blue-700 shadow-sm' : 'text-gray-700 hover:bg-gray-100'"
                            data-item-id="analytics"
                            title="Analytics Dashboard"
                        >
                            <div class="flex items-center justify-center w-5 h-5 rounded transition-colors"
                                 :class="activeItem === 'analytics' ? 'text-blue-600' : 'text-gray-500 group-hover:text-gray-700'">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
                                    <polyline points="22 7 13.5 15.5 8.5 10.5 2 17"></polyline>
                                    <polyline points="16 7 22 7 22 13"></polyline>
                                </svg>
                            </div>
                            <span class="flex-1 text-left font-medium">Analytics</span>
                            <div class="transition-all duration-200"
                                 :class="activeItem === 'analytics' ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-2'">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4 text-blue-600">
                                    <polyline points="9 18 15 12 9 6"></polyline>
                                </svg>
                            </div>
                            <div class="fuse-sidebar-hover absolute inset-0 bg-gradient-to-r from-blue-50/50 to-transparent transition-opacity duration-200"
                                 :class="hoveredItem === 'analytics' && activeItem !== 'analytics' ? 'opacity-100' : 'opacity-0'"></div>
                        </button>

                        <!-- Finance -->
                        <button
                            @click="setActiveItem('finance')"
                            @mouseenter="setHoveredItem('finance')"
                            @mouseleave="clearHoveredItem()"
                            class="fuse-sidebar-item w-full flex items-center gap-3 px-3 py-2.5 text-sm rounded-lg transition-all duration-200 group relative overflow-hidden"
                            :class="activeItem === 'finance' ? 'bg-blue-50 text-blue-700 shadow-sm' : 'text-gray-700 hover:bg-gray-100'"
                            data-item-id="finance"
                            title="Finance Dashboard"
                        >
                            <div class="flex items-center justify-center w-5 h-5 rounded transition-colors"
                                 :class="activeItem === 'finance' ? 'text-blue-600' : 'text-gray-500 group-hover:text-gray-700'">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
                                    <line x1="12" y1="1" x2="12" y2="23"></line>
                                    <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                                </svg>
                            </div>
                            <span class="flex-1 text-left font-medium">Finance</span>
                            <div class="transition-all duration-200"
                                 :class="activeItem === 'finance' ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-2'">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4 text-blue-600">
                                    <polyline points="9 18 15 12 9 6"></polyline>
                                </svg>
                            </div>
                            <div class="fuse-sidebar-hover absolute inset-0 bg-gradient-to-r from-blue-50/50 to-transparent transition-opacity duration-200"
                                 :class="hoveredItem === 'finance' && activeItem !== 'finance' ? 'opacity-100' : 'opacity-0'"></div>
                        </button>

                        <!-- Crypto -->
                        <button
                            @click="setActiveItem('crypto')"
                            @mouseenter="setHoveredItem('crypto')"
                            @mouseleave="clearHoveredItem()"
                            class="fuse-sidebar-item w-full flex items-center gap-3 px-3 py-2.5 text-sm rounded-lg transition-all duration-200 group relative overflow-hidden"
                            :class="activeItem === 'crypto' ? 'bg-blue-50 text-blue-700 shadow-sm' : 'text-gray-700 hover:bg-gray-100'"
                            data-item-id="crypto"
                            title="Crypto Dashboard"
                        >
                            <div class="flex items-center justify-center w-5 h-5 rounded transition-colors"
                                 :class="activeItem === 'crypto' ? 'text-blue-600' : 'text-gray-500 group-hover:text-gray-700'">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
                                    <path d="M11.767 19.089c4.924.868 6.14-6.025 1.216-6.894m-1.216 6.894L5.52 18.921m6.247.168c-4.924-.869-6.14 6.025-1.216 6.893m1.216-6.893l6.247.168"></path>
                                    <path d="M13.983 5.081c-4.924-.868-6.14 6.025-1.216 6.894m1.216-6.894l6.247-.168m-6.247.168c4.924.869 6.14-6.025 1.216-6.893m-1.216 6.893l-6.247-.168"></path>
                                </svg>
                            </div>
                            <span class="flex-1 text-left font-medium">Crypto</span>
                            <div class="transition-all duration-200"
                                 :class="activeItem === 'crypto' ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-2'">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4 text-blue-600">
                                    <polyline points="9 18 15 12 9 6"></polyline>
                                </svg>
                            </div>
                            <div class="fuse-sidebar-hover absolute inset-0 bg-gradient-to-r from-blue-50/50 to-transparent transition-opacity duration-200"
                                 :class="hoveredItem === 'crypto' && activeItem !== 'crypto' ? 'opacity-100' : 'opacity-0'"></div>
                        </button>
                    </nav>
                </div>

                <div class="mx-4 border-t border-gray-200"></div>

                <!-- Applications Section -->
                <div class="p-4">
                    <div class="mb-3">
                        <h3 class="text-xs font-semibold text-gray-500 uppercase tracking-wider">
                            Applications
                        </h3>
                        <p class="text-xs text-gray-400 mt-1">
                            Custom made application designs
                        </p>
                    </div>
                    <nav class="space-y-1">
                        <!-- AI Image Generator -->
                        <button
                            @click="setActiveItem('ai-image')"
                            @mouseenter="setHoveredItem('ai-image')"
                            @mouseleave="clearHoveredItem()"
                            class="fuse-sidebar-item w-full flex items-center gap-3 px-3 py-2.5 text-sm rounded-lg transition-all duration-200 group relative overflow-hidden"
                            :class="activeItem === 'ai-image' ? 'bg-blue-50 text-blue-700 shadow-sm' : 'text-gray-700 hover:bg-gray-100'"
                            data-item-id="ai-image"
                            title="AI Image Generator"
                        >
                            <div class="flex items-center justify-center w-5 h-5 rounded transition-colors"
                                 :class="activeItem === 'ai-image' ? 'text-blue-600' : 'text-gray-500 group-hover:text-gray-700'">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
                                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                                    <circle cx="8.5" cy="8.5" r="1.5"></circle>
                                    <polyline points="21 15 16 10 5 21"></polyline>
                                </svg>
                            </div>
                            <div class="flex-1 text-left min-w-0">
                                <div class="flex items-center gap-2">
                                    <span class="font-medium truncate">AI Image Generator</span>
                                    <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-700 border border-blue-200 flex-shrink-0">NEW</span>
                                </div>
                            </div>
                            <div class="transition-all duration-200 flex-shrink-0"
                                 :class="activeItem === 'ai-image' ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-2'">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4 text-blue-600">
                                    <polyline points="9 18 15 12 9 6"></polyline>
                                </svg>
                            </div>
                            <div class="fuse-sidebar-hover absolute inset-0 bg-gradient-to-r from-blue-50/50 to-transparent transition-opacity duration-200"
                                 :class="hoveredItem === 'ai-image' && activeItem !== 'ai-image' ? 'opacity-100' : 'opacity-0'"></div>
                        </button>

                        <!-- Academy -->
                        <button
                            @click="setActiveItem('academy')"
                            @mouseenter="setHoveredItem('academy')"
                            @mouseleave="clearHoveredItem()"
                            class="fuse-sidebar-item w-full flex items-center gap-3 px-3 py-2.5 text-sm rounded-lg transition-all duration-200 group relative overflow-hidden"
                            :class="activeItem === 'academy' ? 'bg-blue-50 text-blue-700 shadow-sm' : 'text-gray-700 hover:bg-gray-100'"
                            data-item-id="academy"
                            title="Academy"
                        >
                            <div class="flex items-center justify-center w-5 h-5 rounded transition-colors"
                                 :class="activeItem === 'academy' ? 'text-blue-600' : 'text-gray-500 group-hover:text-gray-700'">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
                                    <path d="M22 10v6M2 10l10-5 10 5-10 5z"></path>
                                    <path d="M6 12v5c3 3 9 3 12 0v-5"></path>
                                </svg>
                            </div>
                            <span class="flex-1 text-left font-medium truncate">Academy</span>
                            <div class="transition-all duration-200 flex-shrink-0"
                                 :class="activeItem === 'academy' ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-2'">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4 text-blue-600">
                                    <polyline points="9 18 15 12 9 6"></polyline>
                                </svg>
                            </div>
                            <div class="fuse-sidebar-hover absolute inset-0 bg-gradient-to-r from-blue-50/50 to-transparent transition-opacity duration-200"
                                 :class="hoveredItem === 'academy' && activeItem !== 'academy' ? 'opacity-100' : 'opacity-0'"></div>
                        </button>

                        <!-- Calendar -->
                        <button
                            @click="setActiveItem('calendar')"
                            @mouseenter="setHoveredItem('calendar')"
                            @mouseleave="clearHoveredItem()"
                            class="fuse-sidebar-item w-full flex items-center gap-3 px-3 py-2.5 text-sm rounded-lg transition-all duration-200 group relative overflow-hidden"
                            :class="activeItem === 'calendar' ? 'bg-blue-50 text-blue-700 shadow-sm' : 'text-gray-700 hover:bg-gray-100'"
                            data-item-id="calendar"
                            title="Calendar"
                        >
                            <div class="flex items-center justify-center w-5 h-5 rounded transition-colors"
                                 :class="activeItem === 'calendar' ? 'text-blue-600' : 'text-gray-500 group-hover:text-gray-700'">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
                                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                                    <line x1="16" y1="2" x2="16" y2="6"></line>
                                    <line x1="8" y1="2" x2="8" y2="6"></line>
                                    <line x1="3" y1="10" x2="21" y2="10"></line>
                                </svg>
                            </div>
                            <div class="flex-1 text-left min-w-0">
                                <div class="flex items-center gap-2">
                                    <span class="font-medium truncate">Calendar</span>
                                </div>
                                <p class="text-xs text-gray-500 mt-0.5 truncate">3 upcoming events</p>
                            </div>
                            <div class="transition-all duration-200 flex-shrink-0"
                                 :class="activeItem === 'calendar' ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-2'">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4 text-blue-600">
                                    <polyline points="9 18 15 12 9 6"></polyline>
                                </svg>
                            </div>
                            <div class="fuse-sidebar-hover absolute inset-0 bg-gradient-to-r from-blue-50/50 to-transparent transition-opacity duration-200"
                                 :class="hoveredItem === 'calendar' && activeItem !== 'calendar' ? 'opacity-100' : 'opacity-0'"></div>
                        </button>

                        <!-- Más items de Applications... -->
                        <!-- Messenger, Contacts, E-Commerce, File Manager, Help Center -->
                        <!-- (Agrega los demás items siguiendo el mismo patrón) -->
                    </nav>
                </div>
            </div>

            <!-- Footer -->
            <div class="border-t border-gray-200 bg-white p-4">
                <!-- Help Section -->
                <div class="mb-4">
                    <p class="text-sm text-gray-600 mb-2 leading-relaxed">
                        Need assistance to get started?
                    </p>
                    <button 
                        onclick="window.open('{{ route('docs') }}', '_blank')"
                        class="w-full flex items-center gap-2 px-2 py-2 text-sm text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors font-normal"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="w-4 h-4">
                            <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                            <polyline points="15 3 21 3 21 9"></polyline>
                            <line x1="10" y1="14" x2="21" y2="3"></line>
                        </svg>
                        <span>View documentation</span>
                    </button>
                </div>

                <div class="mb-4 border-t border-gray-200"></div>

                <!-- Contact Info -->
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center ring-2 ring-gray-100">
                        <span class="text-white text-xs font-medium">AK</span>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 truncate leading-tight">
                            Abbott Keitch
                        </p>
                        <p class="text-xs text-gray-500 truncate leading-tight">
                            <EMAIL>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <main class="flex-1 flex items-center justify-center p-8">
            <div class="text-center max-w-2xl">
                <h1 class="text-4xl font-bold text-gray-900 mb-4">
                    Welcome to FUSE React - Laravel Version
                </h1>
                <p class="text-xl text-gray-600 mb-8">
                    This is a recreation of the FUSE React sidebar interface using Laravel and Alpine.js
                </p>
                <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
                    <p class="text-gray-700 mb-4">
                        <strong>Current Active Item:</strong> <span class="text-blue-600" x-text="activeItem"></span>
                    </p>
                    <p class="text-gray-700 mb-4">
                        <strong>URL Segment:</strong> {{ request()->segment(1) ?? 'home' }}
                    </p>
                    <p class="text-gray-500">
                        Select an option from the sidebar to navigate through different sections
                    </p>
                </div>
                @if (Auth::check())
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                        <p class="text-green-800">
                            Welcome, {{ Auth::user()->name }}!
                        </p>
                    </div>
                @endif
            </div>
        </main>
    </div>
</body>
</html>