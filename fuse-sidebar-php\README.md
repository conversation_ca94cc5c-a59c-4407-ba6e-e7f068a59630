# FUSE React Sidebar - PHP Version

Esta es una implementación en PHP del sidebar de FUSE React que mantiene exactamente el mismo diseño y funcionalidad que la versión original en React.

## ✨ Características

- 🎨 **Diseño idéntico** al sidebar de FUSE React
- 🔄 **Interactividad completa** con estados activos y efectos hover
- 📱 **Responsive design** con Tailwind CSS
- 🔧 **Fácil integración** en cualquier proyecto PHP
- 🎯 **Componente reutilizable** y personalizable
- 🚀 **Sin dependencias pesadas** - solo Tailwind CSS y JavaScript vanilla

## 📁 Estructura de Archivos

```
fuse-sidebar-php/
├── index.php              # Componente principal con demo
├── FuseSidebar.class.php  # Clase del componente
├── sidebar.css           # Estilos CSS (opcional)
├── sidebar.js            # JavaScript interactivo (opcional)
├── laravel-example.blade.php # Ejemplo para Laravel
├── wordpress-example.php # Ejemplo para WordPress
└── README.md             # Esta documentación
```

## 🚀 Instalación y Uso

### 1. Uso básico (Standalone)

```php
<?php
require_once 'FuseSidebar.class.php';

// Crear instancia del sidebar
$sidebar = new FuseSidebar('project'); // 'project' es el item activo por defecto

// Mostrar solo el sidebar
echo $sidebar->render();

// O mostrar el demo completo con HTML, CSS y JS
echo $sidebar->renderWithAssets();
?>
```

### 2. Integración en Laravel

```blade
<!-- En tu archivo Blade -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title')</title>
    @stack('styles')
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        @include('partials.sidebar')
        <main class="flex-1">
            @yield('content')
        </main>
    </div>
    @stack('scripts')
</body>
</html>
```

```php
<!-- resources/views/partials/sidebar.blade.php -->
<?php
$sidebar = new FuseSidebar(request()->segment(1) ?? 'project');
echo $sidebar->render();
?>
```

### 3. Integración en WordPress

```php
<?php
// functions.php
require_once get_template_directory() . '/fuse-sidebar-php/FuseSidebar.class.php';

function render_fuse_sidebar($active_item = 'project') {
    $sidebar = new FuseSidebar($active_item);
    return $sidebar->render();
}
?>
```

```php
<!-- sidebar.php -->
<div class="fuse-sidebar-container">
    <?php echo render_fuse_sidebar(get_query_var('pagename') ?? 'project'); ?>
</div>
```

## 🎨 Personalización

### Cambiar el item activo

```php
$sidebar = new FuseSidebar('analytics'); // 'analytics' estará activo
```

### Personalizar menús

```php
$sidebar = new FuseSidebar('project');

// Puedes extender la clase para personalizar los menús
class CustomFuseSidebar extends FuseSidebar {
    protected function getMenuItems() {
        $items = parent::getMenuItems();
        
        // Agregar nuevo item
        $items['applications'][] = [
            'id' => 'custom-item',
            'label' => 'Custom Item',
            'icon' => 'star',
            'title' => 'Custom Item'
        ];
        
        return $items;
    }
}
```

### Cambiar colores

```php
<style>
.fuse-sidebar {
    /* Personalizar colores aquí */
    --fuse-primary: #3b82f6;
    --fuse-primary-hover: #2563eb;
    --fuse-text-primary: #1f2937;
    --fuse-text-secondary: #6b7280;
}
</style>
```

## 🔧 Métodos Disponibles

### `__construct($activeItem = 'project')`
- `$activeItem`: ID del item que estará activo por defecto

### `render()`
- Retorna el HTML del sidebar
- No incluye HTML, CSS o JS adicionales

### `renderWithAssets()`
- Retorna una página HTML completa con el sidebar
- Incluye Tailwind CSS y JavaScript necesario
- Ideal para demostración o uso standalone

## 📱 Items del Menú

### Dashboards
- `project` - Project
- `analytics` - Analytics  
- `finance` - Finance
- `crypto` - Crypto

### Applications
- `ai-image` - AI Image Generator (con badge NEW)
- `academy` - Academy
- `calendar` - Calendar (con subtitle)
- `messenger` - Messenger
- `contacts` - Contacts
- `ecommerce` - E-Commerce
- `file-manager` - File Manager
- `help-center` - Help Center

## 🎯 Eventos JavaScript

El sidebar incluye eventos JavaScript que puedes personalizar:

```javascript
// Cuando se hace clic en un item
function setActiveItem(itemId) {
    console.log('Item seleccionado:', itemId);
    
    // Agregar tu lógica de navegación aquí
    // window.location.href = '/' + itemId;
    
    // O cargar contenido dinámico
    // loadContent(itemId);
}

// Cuando el mouse pasa sobre un item
function setHoveredItem(itemId) {
    console.log('Hover en:', itemId);
}
```

## 🌐 Navegación

Para implementar la navegación, puedes modificar la función `setActiveItem`:

```javascript
function setActiveItem(itemId) {
    // Navegación simple
    window.location.href = '/' + itemId;
    
    // O AJAX para Single Page Application
    fetch('/' + itemId)
        .then(response => response.text())
        .then(html => {
            document.querySelector('main').innerHTML = html;
        });
}
```

## 🔒 Seguridad

El componente incluye:
- Escapado de HTML con `htmlspecialchars()`
- Validación de IDs de items
- Protección contra XSS en los títulos y subtítulos

## 🚀 Rendimiento

- **CSS**: Solo 2KB adicionales a Tailwind CSS
- **JavaScript**: ~1KB de código vanilla
- **HTML**: ~15KB sin comprimir
- **Sin dependencias externas** excepto Tailwind CSS (opcional)

## 📝 Ejemplos de Integración

### CodeIgniter
```php
<?php
// application/views/sidebar.php
require_once APPPATH . 'libraries/FuseSidebar.class.php';
$sidebar = new FuseSidebar($this->uri->segment(1));
echo $sidebar->render();
?>
```

### Symfony
```php
{# templates/sidebar.html.twig #}
{{ include('fuse-sidebar-php/FuseSidebar.class.php') }}
{% set sidebar = new FuseSidebar(app.request.attributes.get('_route')) %}
{{ sidebar.render()|raw }}
```

### CakePHP
```php
<?php
// src/Template/Element/sidebar.ctp
$sidebar = new FuseSidebar($this->request->getParam('controller'));
echo $sidebar->render();
?>
```

## 🤝 Contribuciones

Si encuentras algún problema o quieres mejorar el componente, siéntete libre de abrir un issue o enviar un pull request.

## 📄 Licencia

Este proyecto está bajo la Licencia MIT - ver el archivo LICENSE para detalles.

---

**Nota**: Este componente es una recreación fiel del diseño de FUSE React para uso en proyectos PHP. Mantiene la misma estética y funcionalidad que la versión original.