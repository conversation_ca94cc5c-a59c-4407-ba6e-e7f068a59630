"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { FuseSidebar } from "@/components/fuse-sidebar"
import { FuseHeader } from "@/components/fuse-header"

export default function Home() {
  const router = useRouter()

  useEffect(() => {
    // Redirigir al dashboard de project por defecto
    router.push("/dashboard/project")
  }, [router])

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Mostrar layout mientras se redirige */}
      <FuseSidebar activeItem="project" onNavigation={() => {}} />
      <div className="flex-1 flex flex-col">
        <FuseHeader />
        <main className="flex-1 flex items-center justify-center p-8">
          <div className="text-center max-w-2xl">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Welcome to FUSE React
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Redirecting to Project Dashboard...
            </p>
            <div className="relative w-32 h-32 mx-auto mb-8">
              <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
            </div>
            <p className="text-gray-500">
              If you're not redirected automatically, please click on Project in the sidebar.
            </p>
          </div>
        </main>
      </div>
    </div>
  )
}