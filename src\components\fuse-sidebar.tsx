"use client"

import { useState } from "react"
import { 
  LayoutDashboard, 
  TrendingUp, 
  DollarSign, 
  Bitcoin,
  Image as ImageIcon,
  GraduationCap,
  Calendar,
  MessageCircle,
  Users,
  ShoppingCart,
  FolderOpen,
  HelpCircle,
  ChevronRight,
  ExternalLink
} from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

interface FuseSidebarProps {
  activeItem: string
  onNavigation: (itemId: string) => void
}

export function FuseSidebar({ activeItem, onNavigation }: FuseSidebarProps) {
  const [hoveredItem, setHoveredItem] = useState<string | null>(null)

  const menuItems = {
    dashboards: [
      { id: "project", label: "Produccion", icon: LayoutDashboard },
      { id: "analytics", label: "Calidad Reactiva", icon: TrendingUp },
      { id: "finance", label: "Logistica", icon: DollarSign },
      { id: "crypto", label: "Desafio Tecnico", icon: Bitcoin },
    ],
    applications: [
      { id: "ai-image", label: "AI Image Generator", icon: ImageIcon, isNew: true },
      { id: "academy", label: "Academy", icon: GraduationCap },
      { id: "calendar", label: "Calendar", icon: Calendar, subtitle: "3 upcoming events" },
      { id: "messenger", label: "Messenger", icon: MessageCircle },
      { id: "contacts", label: "Contacts", icon: Users },
      { id: "ecommerce", label: "E-Commerce", icon: ShoppingCart },
      { id: "mysql", label: "MySQL Database", icon: LayoutDashboard },
      { id: "file-manager", label: "File Manager", icon: FolderOpen },
      { id: "help-center", label: "Help Center", icon: HelpCircle },
    ]
  }

  return (
    <div className="w-64 bg-white border-r border-gray-200 flex flex-col h-full shadow-sm">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 bg-white">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center shadow-sm">
              <span className="text-white font-bold text-sm">F</span>
            </div>
            <span className="font-semibold text-gray-900 text-sm">FUSE React</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-6 h-6 bg-black rounded flex items-center justify-center shadow-sm">
              <span className="text-white text-xs font-bold">N</span>
            </div>
            <span className="text-xs text-gray-600 font-medium">Nextjs</span>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto bg-gray-50/30">
        {/* Dashboards Section */}
        <div className="p-4">
          <div className="mb-3">
            <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider">
              Dashboards
            </h3>
            <p className="text-xs text-gray-400 mt-1">
              Unique dashboard designs
            </p>
          </div>
          <nav className="space-y-1">
            {menuItems.dashboards.map((item) => {
              const Icon = item.icon
              const isActive = activeItem === item.id
              const isHovered = hoveredItem === item.id
              
              return (
                <button
                  key={item.id}
                  onClick={() => onNavigation(item.id)}
                  onMouseEnter={() => setHoveredItem(item.id)}
                  onMouseLeave={() => setHoveredItem(null)}
                  className={cn(
                    "w-full flex items-center gap-3 px-3 py-2.5 text-sm rounded-lg transition-all duration-200 group relative overflow-hidden",
                    isActive
                      ? "bg-blue-50 text-blue-700 shadow-sm"
                      : "text-gray-700 hover:bg-gray-100"
                  )}
                >
                  <div className={cn(
                    "flex items-center justify-center w-5 h-5 rounded transition-colors",
                    isActive ? "text-blue-600" : "text-gray-500 group-hover:text-gray-700"
                  )}>
                    <Icon className="w-4 h-4" />
                  </div>
                  <span className="flex-1 text-left font-medium">{item.label}</span>
                  <div className={cn(
                    "transition-all duration-200",
                    isActive ? "opacity-100 translate-x-0" : "opacity-0 -translate-x-2"
                  )}>
                    <ChevronRight className="w-4 h-4 text-blue-600" />
                  </div>
                  {/* Hover effect */}
                  <div className={cn(
                    "absolute inset-0 bg-gradient-to-r from-blue-50/50 to-transparent transition-opacity duration-200",
                    isHovered && !isActive ? "opacity-100" : "opacity-0"
                  )} />
                </button>
              )
            })}
          </nav>
        </div>

        <Separator className="mx-4 bg-gray-200" />

        {/* Applications Section */}
        <div className="p-4">
          <div className="mb-3">
            <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider">
              Applications
            </h3>
            <p className="text-xs text-gray-400 mt-1">
              Custom made application designs
            </p>
          </div>
          <nav className="space-y-1">
            {menuItems.applications.map((item) => {
              const Icon = item.icon
              const isActive = activeItem === item.id
              const isHovered = hoveredItem === item.id
              
              return (
                <button
                  key={item.id}
                  onClick={() => onNavigation(item.id)}
                  onMouseEnter={() => setHoveredItem(item.id)}
                  onMouseLeave={() => setHoveredItem(null)}
                  className={cn(
                    "w-full flex items-center gap-3 px-3 py-2.5 text-sm rounded-lg transition-all duration-200 group relative overflow-hidden",
                    isActive
                      ? "bg-blue-50 text-blue-700 shadow-sm"
                      : "text-gray-700 hover:bg-gray-100"
                  )}
                >
                  <div className={cn(
                    "flex items-center justify-center w-5 h-5 rounded transition-colors",
                    isActive ? "text-blue-600" : "text-gray-500 group-hover:text-gray-700"
                  )}>
                    <Icon className="w-4 h-4" />
                  </div>
                  <div className="flex-1 text-left min-w-0">
                    <div className="flex items-center gap-2">
                      <span className="font-medium truncate">{item.label}</span>
                      {item.isNew && (
                        <Badge 
                          variant="secondary" 
                          className="text-xs px-1.5 py-0.5 bg-blue-100 text-blue-700 hover:bg-blue-200 border-blue-200 flex-shrink-0"
                        >
                          NEW
                        </Badge>
                      )}
                    </div>
                    {item.subtitle && (
                      <p className="text-xs text-gray-500 mt-0.5 truncate">
                        {item.subtitle}
                      </p>
                    )}
                  </div>
                  <div className={cn(
                    "transition-all duration-200 flex-shrink-0",
                    isActive ? "opacity-100 translate-x-0" : "opacity-0 -translate-x-2"
                  )}>
                    <ChevronRight className="w-4 h-4 text-blue-600" />
                  </div>
                  {/* Hover effect */}
                  <div className={cn(
                    "absolute inset-0 bg-gradient-to-r from-blue-50/50 to-transparent transition-opacity duration-200",
                    isHovered && !isActive ? "opacity-100" : "opacity-0"
                  )} />
                </button>
              )
            })}
          </nav>
        </div>
      </div>

      {/* Footer */}
      <div className="border-t border-gray-200 bg-white p-4">
        {/* Help Section */}
        <div className="mb-4">
          <p className="text-sm text-gray-600 mb-2 leading-relaxed">
            Need assistance to get started?
          </p>
          <Button 
            variant="ghost" 
            className="w-full justify-start text-blue-600 hover:text-blue-700 hover:bg-blue-50 p-2 h-auto font-normal"
          >
            <ExternalLink className="w-4 h-4 mr-2 flex-shrink-0" />
            <span className="text-sm">View documentation</span>
          </Button>
        </div>

        <Separator className="mb-4 bg-gray-200" />

        {/* Contact Info */}
        <div className="flex items-center gap-3">
          <Avatar className="w-8 h-8 ring-2 ring-gray-100">
            <AvatarImage src="/placeholder-avatar.jpg" alt="Abbott Keitch" />
            <AvatarFallback className="bg-gradient-to-br from-blue-500 to-blue-600 text-white text-xs font-medium">
              AK
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 truncate leading-tight">
              Abbott Keitch
            </p>
            <p className="text-xs text-gray-500 truncate leading-tight">
              <EMAIL>
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}